import { Component, Input, Output, EventEmitter, inject } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { environment } from '../../../../environments/environment';
import { TableUtilsService } from '../../../services/table-utils.service';

// Angular Material imports
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-form-actions',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule
  ],
  templateUrl: './form-actions.component.html',
  styleUrl: './form-actions.component.scss'
})
export class FormActionsComponent {
  @Input() form!: FormGroup;
  @Input() tableName!: string;
  @Input() screenName!: string;
  @Input() isTenantBasedFlag: boolean = false;
  @Input() authorizeNumber: number = 1;
  @Input() isViewMode: boolean = false;
  @Input() showSuccessPopup: boolean = false;
  @Input() successMessage: string = '';
  @Input() errorMessage: string = '';
  @Input() isLoading: boolean = false;
  @Input() validationResult: any;
  @Input() fields: any[] = [];
  @Input() hasSubScreens: boolean = false;
  @Input() subScreenForms: { [key: string]: FormGroup } = {};
  @Input() subScreens: string[] = [];
  @Input() subScreensMetadata: any[] = [];

  @Output() submissionSuccess = new EventEmitter<boolean>();
  @Output() errorMessageChange = new EventEmitter<string>();
  @Output() isLoadingChange = new EventEmitter<boolean>();
  @Output() showSuccessPopupChange = new EventEmitter<boolean>();
  @Output() successMessageChange = new EventEmitter<string>();
  @Output() validationResultChange = new EventEmitter<any>();
  @Output() goBackRequested = new EventEmitter<void>();
  @Output() setFormReadonly = new EventEmitter<boolean>();
  @Output() populateForm = new EventEmitter<any>();
  @Output() populateDefaultFields = new EventEmitter<any[]>();
  @Output() setViewMode = new EventEmitter<boolean>();

  private http = inject(HttpClient);
  private tableUtilsService = inject(TableUtilsService);

  /**
   * Collects all form data including main form and subscreen forms
   * @returns Combined form data object
   */
  private getAllFormData(): any {
    // Get main form data
    const mainFormData = this.form.getRawValue();

    // If no subscreens, return main form data only
    if (!this.hasSubScreens || !this.subScreens || this.subScreens.length === 0) {
      return mainFormData;
    }

    // Collect subscreen data
    const allFormData = { ...mainFormData };

    this.subScreens.forEach(subScreenId => {
      const subScreenForm = this.subScreenForms[subScreenId];
      if (subScreenForm) {
        const subScreenData = subScreenForm.getRawValue();
        allFormData[subScreenId] = subScreenData;
      }
    });

    return allFormData;
  }

  /**
   * Validates all forms (main form + subscreen forms)
   * @returns True if all forms are valid
   */
  private areAllFormsValid(): boolean {
    // Check main form validity
    if (!this.form.valid) {
      return false;
    }

    // Check subscreen forms validity
    if (this.hasSubScreens && this.subScreens) {
      for (const subScreenId of this.subScreens) {
        const subScreenForm = this.subScreenForms[subScreenId];
        if (subScreenForm && !subScreenForm.valid) {
          return false;
        }
      }
    }

    return true;
  }

  authorizeRecord() {
    this.errorMessageChange.emit("");
    this.isLoadingChange.emit(true);

    const id = this.form.get("ID")?.value;
    const tableNameToUse = this.tableName || this.screenName;
    const tablesApiId = this.tableUtilsService.extractTablesApiId(tableNameToUse);
    const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/records/${id}/authorize`;
    const params = {
      isTenantBased: this.isTenantBasedFlag.toString(),
      authorizeNumber: this.authorizeNumber
    };
    this.http.put(apiUrl, {}, { withCredentials: true, params }).subscribe({
      next: (response: any) => {
        if (response && response.status === "success") {
          this.showSuccessPopupChange.emit(true);
          this.successMessageChange.emit("Record authorized successfully!");
          this.setViewMode.emit(false);
          this.setFormReadonly.emit(false);
          this.goBackRequested.emit();
          setTimeout(() => {
            this.showSuccessPopupChange.emit(false);
          }, 20000);
        } else {
          this.errorMessageChange.emit(response.message || "Authorization failed");
        }
      },
      error: (error) => {
        this.errorMessageChange.emit("An error occurred during authorization");
      },
      complete: () => this.isLoadingChange.emit(false)
    });
  }

  onSubmit() {
    this.errorMessageChange.emit("");

    if (this.areAllFormsValid()) {
      const tableNameToUse = this.tableName || this.screenName;
      const tablesApiId = this.tableUtilsService.extractTablesApiId(tableNameToUse);
      const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/records`;
      
      const rawFormData = this.getAllFormData();
      
      const formData = this.buildFormData(rawFormData);
      
      const params = {
        isTenantBased: this.isTenantBasedFlag.toString(),
        authorizeNumber: this.authorizeNumber
      };
      this.http.post(apiUrl, formData, { withCredentials: true, params }).subscribe({
        next: (response: any) => {
          if (response.status === "success") {
            this.showSuccessPopupChange.emit(true);
            this.successMessageChange.emit("Record submitted successfully!");
            this.goBackRequested.emit();
            setTimeout(() => {
              this.showSuccessPopupChange.emit(false);
            }, 20000);
          } else if (response.status === "error") {
            this.errorMessageChange.emit(response.message[0].error || "An error occurred while submitting the form");
          }
        },
        error: (error: any) => {
          if (error.error && error.error.message) {
            this.errorMessageChange.emit(error.error.message[0].error);
          } else {
            this.errorMessageChange.emit("An unexpected error occurred while submitting the form");
          }
        }
      });
    } else {
      this.errorMessageChange.emit("Please fill in all required fields in the main form and all subscreens before submitting.");
    }
  }

  validateRecord() {
    this.errorMessageChange.emit("");
    this.isLoadingChange.emit(true);

    if (this.areAllFormsValid()) {
      const tableNameToUse = this.tableName || this.screenName;
      const tablesApiId = this.tableUtilsService.extractTablesApiId(tableNameToUse);
      const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/validate`;
      const formData = this.buildFormData(this.getAllFormData());
      const params = {
        isTenantBased: this.isTenantBasedFlag.toString(),
        authorizeNumber: this.authorizeNumber
      };
      this.http.post(apiUrl, formData, { withCredentials: true, params }).subscribe({
        next: (response: any) => {
          if (response.status === "success") {
            this.validationResultChange.emit(response.data);
            this.populateForm.emit(response.data);
            // Handle defaultFields even in error response
            if (response.defaultFields && Array.isArray(response.defaultFields)) {
              this.populateDefaultFields.emit(response.defaultFields);
            }
          } else if (response.status === "error") {
            this.validationResultChange.emit(response.data);
            this.errorMessageChange.emit(response.message[0].error || "An error occurred during validation");
            this.populateForm.emit(response.data);
            // Handle defaultFields if present in validation response
            if (response.defaultFields && Array.isArray(response.defaultFields)) {
              this.populateDefaultFields.emit(response.defaultFields);
            }
          }
        },
        error: (error: any) => {
          this.validationResultChange.emit(error.error?.data || null);
          this.errorMessageChange.emit(error.error?.message[0].error || "An unexpected error occurred during validation");

          if (error.error?.data) {
            this.populateForm.emit(error.error.data);
          }
        },
        complete: () => this.isLoadingChange.emit(false)
      });
    } else {
      this.errorMessageChange.emit("Please fill in all required fields in the main form and all subscreens before validation.");
      this.isLoadingChange.emit(false);
    }
  }

  closeSuccessPopup() {
    this.showSuccessPopupChange.emit(false);
  }

  
  onRejectRecord() {
  this.errorMessageChange.emit("");
  this.isLoadingChange.emit(true);

  const id = this.form.get("ID")?.value;
  const tableNameToUse = this.tableName || this.screenName;
  const tablesApiId = this.tableUtilsService.extractTablesApiId(tableNameToUse);
  const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/records/${id}/reject`;

  const params = {
    isTenantBased: this.isTenantBasedFlag.toString(),
    authorizeNumber: this.authorizeNumber
  };

  

  this.http.put(apiUrl, {}, {withCredentials: true, params}).subscribe({
    next: (response: any) => {
      if (response && response.status === "success") {
        this.showSuccessPopupChange.emit(true);
        this.successMessageChange.emit("Record rejected successfully!");
        this.setViewMode.emit(false);
        this.setFormReadonly.emit(false);
        this.goBackRequested.emit();
        setTimeout(() => {
          this.showSuccessPopupChange.emit(false);
        }, 20000);
      } else {
        this.errorMessageChange.emit(response.message || "Rejection failed");
      }
    },
    error: () => {
      this.errorMessageChange.emit("An error occurred during rejection");
    },
    complete: () => this.isLoadingChange.emit(false)
  });
}

  

  onDeleteRecord() {
  this.errorMessageChange.emit("");
  this.isLoadingChange.emit(true);

  const id = this.form.get("ID")?.value;
  const tableNameToUse = this.tableName || this.screenName;
  const tablesApiId = this.tableUtilsService.extractTablesApiId(tableNameToUse);
  const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/records/${id}`;

  const params = {
    isTenantBased: this.isTenantBasedFlag.toString(),
    authorizeNumber: this.authorizeNumber
  };

  

  this.http.delete(apiUrl, { withCredentials: true, params }).subscribe({
    next: (response: any) => {
      if (response && response.status === "success") {
        this.showSuccessPopupChange.emit(true);
        this.successMessageChange.emit("Record deleted successfully!");
        this.setViewMode.emit(false);
        this.setFormReadonly.emit(false);
        this.goBackRequested.emit();
        setTimeout(() => {
          this.showSuccessPopupChange.emit(false);
        }, 20000);
      } else {
        this.errorMessageChange.emit(response.message || "Deletion failed");
      }
    },
    error: (error) => {
      this.errorMessageChange.emit("An error occurred during deletion");
    },
    complete: () => this.isLoadingChange.emit(false)
  });
}


  private buildFormData(data: any): any {
    const result: { [key: string]: any } = {};
    
    // First, process main form fields (non-subscreen fields)
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        const value = data[key];
        // Skip null/undefined values
        if (value === null || value === undefined) continue;

        // Clean field names by trimming whitespace
        const cleanedKey = key.trim();

        // Skip subscreen fields for now - we'll process them separately
        if (this.hasSubScreens && this.subScreens.includes(cleanedKey)) {
          continue;
        }

        // Find field definition for type conversion
        const field = this.fields.find(field => 
          field.fieldName === cleanedKey || field.fieldName === key
        );

        // Process main form fields
        if (Array.isArray(value)) {
          if (field && field.isMulti) {
            // Filter out empty objects from multi-fields
            const filteredArray = value.filter((item: any) => {
              if (typeof item === 'object' && item !== null) {
                // Check if object has any non-empty values
                return Object.values(item).some(val => val !== null && val !== undefined && val !== "");
              }
              return item !== null && item !== undefined && item !== "";
            });
            
            if (filteredArray.length > 0) {
              if (typeof filteredArray[0] === 'object' && filteredArray[0][field.fieldName] !== undefined) {
                result[cleanedKey] = filteredArray.map((item: any) =>
                  this.convertValueByType(item[field.fieldName], field)
                );
              } else {
                result[cleanedKey] = filteredArray.map((item: any) =>
                  this.convertValueByType(item, field)
                );
              }
            }
          } else {
            const nestedData = value.map((item: any) => {
              if (item.fieldsToAppear) {
                return item.fieldsToAppear;
              } else {
                return this.buildFormData(item);
              }
            }).filter((item: any) => {
              // Filter out empty objects
              if (typeof item === 'object' && item !== null) {
                return Object.keys(item).length > 0;
              }
              return item !== null && item !== undefined;
            });
            
            if (nestedData.length > 0) {
              result[cleanedKey] = nestedData;
            }
          }
        } else if (typeof value === "object") {
          const nestedObject = this.buildFormData(value);
          if (Object.keys(nestedObject).length > 0) {
            result[cleanedKey] = nestedObject;
          }
        } else {
          // Skip empty string values - don't send them at all
          if (value === "" || value === null || value === undefined) {
            continue;
          }
          
          // Apply type conversion based on field type
          const convertedValue = this.convertValueByType(value, field);
          if (convertedValue !== null && convertedValue !== undefined) {
            result[cleanedKey] = convertedValue;
          }
        }
      }
    }

    // Now flatten subscreen data into the root
    if (this.hasSubScreens && this.subScreens) {
      for (const subScreenId of this.subScreens) {
        if (data[subScreenId]) {
          const cleanedSubScreenData = this.processSubScreenData(data[subScreenId]);
          if (Object.keys(cleanedSubScreenData).length > 0) {
            // Flatten subscreen fields into root
            Object.entries(cleanedSubScreenData).forEach(([k, v]) => {
              result[k] = v;
            });
          }
        }
      }
    }

    return result;
  }

  /**
   * Convert value based on field type
   */
  private convertValueByType(value: any, field: any): any {
    if (!field || !field.type) {
      return value;
    }

    // Convert non-empty values based on type
    switch (field.type) {
      case 'int':
        const intValue = parseInt(value, 10);
        return isNaN(intValue) ? null : intValue;
      case 'double':
        const doubleValue = parseFloat(value);
        return isNaN(doubleValue) ? null : doubleValue;
      case 'boolean':
        return Boolean(value);
      case 'date':
        return value; // Keep date as string
      default:
        return value;
    }
  }

  /**
   * Process subscreen data without flattening it
   */
  private processSubScreenData(data: any): any {
    const result: { [key: string]: any } = {};
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        const value = data[key];
        // Skip null/undefined values
        if (value === null || value === undefined) continue;

        // Clean field names by trimming whitespace
        const cleanedKey = key.trim();

        // Find field definition for type conversion from main fields or subscreen metadata
        let field = this.fields.find(field =>
          field.fieldName === cleanedKey || field.fieldName === key
        );

        // If not found in main fields, search in subscreen metadata
        if (!field && this.subScreensMetadata) {
          for (const subScreenMeta of this.subScreensMetadata) {
            if (subScreenMeta.fieldName) {
              field = subScreenMeta.fieldName.find((f: any) =>
                f.fieldName === cleanedKey || f.fieldName === key
              );
              if (field) break;
            }
          }
        }

        if (Array.isArray(value)) {
          if (field && field.isMulti) {
            // Filter out empty objects from multi-fields
            const filteredArray = value.filter((item: any) => {
              if (typeof item === 'object' && item !== null) {
                // Check if object has any non-empty values
                return Object.values(item).some(val => val !== null && val !== undefined && val !== "");
              }
              return item !== null && item !== undefined && item !== "";
            });

            if (filteredArray.length > 0) {
              if (typeof filteredArray[0] === 'object' && filteredArray[0][field.fieldName] !== undefined) {
                result[cleanedKey] = filteredArray.map((item: any) =>
                  this.convertValueByType(item[field.fieldName], field)
                );
              } else {
                result[cleanedKey] = filteredArray.map((item: any) =>
                  this.convertValueByType(item, field)
                );
              }
            }
          } else {
            // Filter out empty objects from arrays
            const filteredArray = value.filter((item: any) => {
              if (typeof item === 'object' && item !== null) {
                // Check if object has any non-empty values
                return Object.values(item).some(val => val !== null && val !== undefined && val !== "");
              }
              return item !== null && item !== undefined && item !== "";
            });

            if (filteredArray.length > 0) {
              result[cleanedKey] = filteredArray;
            }
          }
        } else if (typeof value === "object") {
          const nestedObject = this.processSubScreenData(value);
          if (Object.keys(nestedObject).length > 0) {
            result[cleanedKey] = nestedObject;
          }
        } else {
          // Skip empty string values - don't send them at all
          if (value === "" || value === null || value === undefined) {
            continue;
          }
          
          // Apply type conversion based on field type
          const convertedValue = this.convertValueByType(value, field);
          if (convertedValue !== null && convertedValue !== undefined) {
            result[cleanedKey] = convertedValue;
          }
        }
      }
    }
    return result;
  }

  // Note: extractTablesApiId moved to TableUtilsService
}
