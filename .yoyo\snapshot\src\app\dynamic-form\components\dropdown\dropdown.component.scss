@use '../../../../styles/shared.scss' as *;

/* Dropdown Input Container - Arrow button perfectly attached to textbox */
.dropdown-input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  min-width: 200px;
  border-radius: 8px; /* Match the rounded corners of the integrated component */
}

/* Base form input styling - Seamlessly integrated with arrow button */
.dropdown-input-container .form-input {
  flex: 1;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-radius: 8px 0 0 8px;
  border-right: none;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #222222;
  background: #FFFFFF;
  transition: all 0.3s ease;
  padding: 0 12px;
  padding-right: 0; /* Remove right padding since arrow button is attached */
  outline: none;
  box-sizing: border-box;
}

/* Focus state for entire container - input and button as one component with rounded shadow */
.dropdown-input-container:focus-within {
  box-shadow: 0 0 0 2px rgba(158, 158, 158, 0.25);
  border-radius: 8px; /* Ensure shadow follows the rounded border shape */
}

.dropdown-input-container:focus-within .form-input {
  outline: none;
  border-color: #9e9e9e;
}

.dropdown-input-container:focus-within .dropdown-arrow-btn {
  border-color: #9e9e9e;
}

/* Remove individual input focus styles since container handles it */
.dropdown-input-container .form-input:focus {
  outline: none;
  box-shadow: none;
}

/* Invalid input state for entire container - input and button as one component */
.dropdown-input-container:has(.form-input.invalid-input) {
  box-shadow: none; /* Remove default shadow for invalid state */
  border-radius: 8px; /* Maintain rounded corners for invalid state */
}

.dropdown-input-container:has(.form-input.invalid-input):focus-within {
  box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
  border-radius: 8px; /* Ensure invalid focus shadow follows rounded border shape */
}

.dropdown-input-container:has(.form-input.invalid-input) .form-input {
  border-color: #f44336;
  background-color: rgba(244, 67, 54, 0.05);
}

.dropdown-input-container:has(.form-input.invalid-input) .dropdown-arrow-btn {
  border-color: #f44336;
}

/* Fallback for browsers that don't support :has() */
.dropdown-input-container .form-input.invalid-input {
  border-color: #f44336;
  background-color: rgba(244, 67, 54, 0.05);
}

.dropdown-input-container .form-input.invalid-input:focus {
  box-shadow: none; /* Container handles the shadow */
}

.dropdown-input-container .form-input.invalid-input + .dropdown-arrow-btn {
  border-color: #f44336;
}

/* Placeholder styling - EXACT from backup lines 336-338 */
.dropdown-input-container .form-input::placeholder {
  color: #999;
}

/* Dropdown arrow button - Perfectly attached to textbox end */
.dropdown-input-container .dropdown-arrow-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-left: none;
  border-radius: 0 8px 8px 0;
  background-color: #f8f9fa;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
  margin-left: -1px; /* Overlap with input border for seamless attachment */
  position: relative; /* Changed from absolute to relative for better integration */
  flex-shrink: 0; /* Prevent button from shrinking */
}

.dropdown-input-container .dropdown-arrow-btn:hover {
  background-color: #e9ecef;
  color: #283A97;
}

/* Arrow button focus state - integrated with container focus */
.dropdown-input-container .dropdown-arrow-btn:focus {
  outline: none;
  box-shadow: none; /* Container handles the focus shadow */
}

.dropdown-input-container .dropdown-arrow-btn .mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  line-height: 1;
}

/* Dropdown List Styles - EXACT from regular-field component lines 154-193 */
.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: white;
  border: 1px solid #DBDBDB;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
}

.dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f1f3f4;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  color: #495057;
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
  color: #283A97;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-empty {
  padding: 12px 16px;
  color: #6c757d;
  font-style: italic;
  text-align: center;
}

.dropdown-loading {
  padding: 12px 16px;
  color: #6c757d;
  font-style: italic;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.dropdown-loading .mat-icon {
  animation: spin 1s linear infinite;
  font-size: 16px;
  width: 16px;
  height: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Disabled State Styling - EXACT from backup implementation */
.dropdown-input-container.disabled .form-input,
.dropdown-input-container .form-input.disabled,
.dropdown-input-container .form-input[disabled],
.dropdown-input-container .form-input[readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
  pointer-events: none !important;
}

.dropdown-input-container.disabled .dropdown-arrow-btn,
.dropdown-input-container .dropdown-arrow-btn[disabled] {
  background-color: #f1f3f4 !important;
  border-color: #dadce0 !important;
  cursor: not-allowed !important;
  pointer-events: none !important;
}

.dropdown-input-container.disabled .dropdown-arrow-btn .mat-icon,
.dropdown-input-container .dropdown-arrow-btn[disabled] .mat-icon {
  color: #5f6368 !important;
}

/* Ensure dropdown works well in different contexts - EXACT from backup */
.multi-input .dropdown-input-container,
.group-fields .dropdown-input-container {
  width: 100%;
  min-width: 200px;
}

/* Adjust dropdown positioning in grouped fields */
.grouped-field-section .dropdown-list {
  z-index: 1001;
}

/* Ensure proper styling inheritance for all form-input elements */
.form-input {
  height: 40px;
  border: 1px solid #DBDBDB;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #222222;
  background: #FFFFFF;
  transition: all 0.3s ease;
  padding: 0 12px;
  outline: none;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #9e9e9e;
  box-shadow: 0 0 0 2px rgba(158, 158, 158, 0.25);
}

.form-input.invalid-input {
  border-color: #f44336;
  background-color: rgba(244, 67, 54, 0.05);
}

.form-input.invalid-input:focus {
  box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
}

.form-input::placeholder {
  color: #999;
}

/* Responsive dropdown styling - EXACT from regular-field component lines 427-491 */
@media (max-width: 768px) {
  .dropdown-input-container .dropdown-arrow-btn {
    width: 36px;
    height: 36px;
  }

  .dropdown-input-container .dropdown-arrow-btn .mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }

  .dropdown-item {
    padding: 10px 12px;
    font-size: 13px;
  }

  .dropdown-empty {
    padding: 10px 12px;
    font-size: 13px;
  }

  .dropdown-loading {
    padding: 10px 12px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .dropdown-input-container .dropdown-arrow-btn {
    width: 32px;
    height: 32px;
  }

  .dropdown-input-container .dropdown-arrow-btn .mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
  }

  .dropdown-item {
    padding: 8px 10px;
    font-size: 12px;
  }

  .dropdown-empty {
    padding: 8px 10px;
    font-size: 12px;
  }

  .dropdown-loading {
    padding: 8px 10px;
    font-size: 12px;
  }
}

/* Scrollbar styling for dropdown lists - EXACT from regular-field component lines 494-510 */
.dropdown-list::-webkit-scrollbar {
  width: 6px;
}

.dropdown-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.dropdown-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.dropdown-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Ensure dropdown containers work well in multi-field and grouped contexts - EXACT from regular-field component lines 523-532 */
.multi-input .dropdown-input-container,
.group-fields .dropdown-input-container {
  width: 100%;
  min-width: 200px;
}

/* Adjust dropdown positioning in grouped fields */
.grouped-field-section .dropdown-list {
  z-index: 1001;
}
