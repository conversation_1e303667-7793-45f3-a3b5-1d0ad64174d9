import { Injectable, inject } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators } from "@angular/forms";
import { FieldDistributionService } from './field-distribution.service';

@Injectable({
  providedIn: 'root'
})
export class SubScreenManagementService {
  private fb = inject(FormBuilder);
  private fieldDistributionService = inject(FieldDistributionService);

  // SubScreen properties (exact copies from component)
  hasSubScreens: boolean = false;
  subScreens: string[] = [];
  subScreensMetadata: any[] = [];
  subScreenForms: { [key: string]: FormGroup } = {};
  subScreenColumns: { [key: string]: any[][] } = {};

  /**
   * Process SubScreens from metadata
   * EXACT COPY from component method (Lines 310-338)
   */
  processSubScreens(data: any) {
    const validSubScreens = Array.isArray(data.subScreen)
      ? data.subScreen.filter((id: string) => !!id && id.trim() !== "")
      : [];

    if (
      validSubScreens.length > 0 &&
      Array.isArray(data.subScreensMetadata) &&
      data.subScreensMetadata.length > 0
    ) {
      this.hasSubScreens = true;
      this.subScreens = validSubScreens;
      this.subScreensMetadata = data.subScreensMetadata;

      // Process each SubScreen
      this.subScreensMetadata.forEach((subScreenMetadata: any) => {
        this.processSubScreen(subScreenMetadata);
      });
    } else {
      this.hasSubScreens = false;
      this.subScreens = [];
      this.subScreensMetadata = [];
    }
  }

  /**
   * Process individual SubScreen metadata
   * EXACT COPY from component method (Lines 339-376)
   */
  processSubScreen(subScreenMetadata: any) {
    const subScreenId = subScreenMetadata.ID;
    const columnCount = subScreenMetadata.columnNumber || 1;

    // Clean field names by trimming whitespace
    if (subScreenMetadata.fieldName && Array.isArray(subScreenMetadata.fieldName)) {
      subScreenMetadata.fieldName.forEach((field: any) => {
        if (field.fieldName) {
          const originalName = field.fieldName;
          field.fieldName = field.fieldName.trim();
          if (originalName !== field.fieldName) {
          }
        }
      });
    }

    // Order fields using existing method
    const orderedFields = this.fieldDistributionService.orderFieldsBasedOnFormDefinition(subScreenMetadata.fieldName || []);

    // Filter out ID fields and other non-visible fields
    const visibleFields = orderedFields.filter((field: any) =>
      field.fieldName?.toUpperCase() !== 'ID'
    );

    // Distribute fields into columns for this SubScreen
    this.subScreenColumns[subScreenId] = this.fieldDistributionService.distributeFieldsRoundRobin(visibleFields, columnCount);

    // Create form group for this SubScreen
    this.subScreenForms[subScreenId] = this.createSubScreenForm(subScreenMetadata);

    // Debug form controls
    this.debugSubScreenFormControls(subScreenId);
  }

  /**
   * Debug SubScreen form controls
   * EXACT COPY from component method (Lines 377-396)
   */
  debugSubScreenFormControls(subScreenId: string) {
    const form = this.subScreenForms[subScreenId];
    if (form) {
      Object.keys(form.controls).forEach(controlName => {
        const control = form.get(controlName);
        if (control instanceof FormArray) {
          control.controls.forEach((groupControl, index) => {
            if (groupControl instanceof FormGroup) {
            }
          });
        }
      });
    }
  }

  /**
   * Create SubScreen form
   * EXACT COPY from component method (Lines 397-473)
   */
  createSubScreenForm(subScreenMetadata: any): FormGroup {
    const formGroup = this.fb.group({});

    // Add ID field
    formGroup.addControl('ID', this.fb.control(''));

    if (subScreenMetadata.fieldName && Array.isArray(subScreenMetadata.fieldName)) {
      // Process each field
      subScreenMetadata.fieldName.forEach((field: any) => {
        if (field.fieldName !== "ID") {
          if (field.isMulti && !field.Group) {
            // Non-grouped multi-field
            const multiFieldArray = this.fb.array([this.createMultiField(field)]);
            formGroup.addControl(field.fieldName, multiFieldArray);

            // Disable multi-field if noInput is true
            if (field.noInput) {
              multiFieldArray.disable({ emitEvent: false });
            }
          } else if (field.Group) {
            const parsed = this.parseGroupPath(field.Group);
            if (!parsed.isNested) {
              // Direct field of parent group - already handled in addSubScreenGroup
            } else {
              // Nested group field - already handled in addSubScreenGroup
            }
          } else {
            // Regular field
            const validators = field.mandatory ? Validators.required : null;
            let control;
            switch (field.type) {
              case "boolean":
                control = this.fb.control(false, validators);
                break;
              case "date":
                control = this.fb.control(null, validators);
                break;
              default:
                control = this.fb.control("", validators);
                break;
            }
            formGroup.addControl(field.fieldName, control);

            // Disable control if noInput is true
            if (field.noInput) {
              control.disable({ emitEvent: false });
            }
          }
        }
      });

      // Handle grouped fields
      const groupNames = [...new Set(
        subScreenMetadata.fieldName
          .filter((field: any) => field.Group)
          .map((field: any) => this.parseGroupPath(field.Group).parent)
          .filter((name: any) => name) // Filter out null/undefined values
      )] as string[];

      groupNames.forEach((groupName: string) => {
        if (groupName && !formGroup.get(groupName)) {
          const groupArray = this.fb.array([this.createSubScreenGroup(subScreenMetadata, groupName)]);
          formGroup.addControl(groupName, groupArray);
        }
      });
    }

    return formGroup;
  }

  /**
   * Create SubScreen group
   * EXACT COPY from component method (Lines 474-510)
   */
  createSubScreenGroup(subScreenMetadata: any, groupName: string): FormGroup {
    const group = this.fb.group({});

    // Handle nested groups
    const parsed = this.parseGroupPath(groupName);
    if (parsed.isNested && parsed.parent && parsed.child) {
      // This is a nested group, create fields for this specific path
      this.getSubScreenFieldsForGroupPath(subScreenMetadata.ID, groupName).forEach((field: any) => {
        this.addFieldToGroup(group, field);
      });
    } else {
      // This is a parent group, create fields and nested subgroups
      this.getSubScreenFieldsForGroup(subScreenMetadata.ID, groupName).forEach((field: any) => {
        const fieldParsed = this.parseGroupPath(field.Group);
        if (!fieldParsed.isNested) {
          // Direct field of this group
          this.addFieldToGroup(group, field);
        }
      });

      // Add nested subgroups
      const childGroups = this.getSubScreenChildGroups(subScreenMetadata, groupName);
      childGroups.forEach(childGroup => {
        const childGroupArray = this.fb.array([this.createSubScreenGroup(subScreenMetadata, `${groupName}|${childGroup}`)]);
        group.addControl(childGroup, childGroupArray);
      });
    }

    return group;
  }

  /**
   * Get SubScreen child groups
   * EXACT COPY from component method (Lines 511-529)
   */
  getSubScreenChildGroups(subScreenMetadata: any, parentGroup: string): string[] {
    const childGroups = new Set<string>();
    if (subScreenMetadata.fieldName) {
      subScreenMetadata.fieldName.forEach((field: any) => {
        if (field.Group) {
          const parsed = this.parseGroupPath(field.Group);
          if (parsed.parent === parentGroup.trim() && parsed.child) {
            childGroups.add(parsed.child);
          }
        }
      });
    }
    return Array.from(childGroups);
  }

  /**
   * Get SubScreen form
   * EXACT COPY from component method (Lines 539-547)
   */
  getSubScreenForm(subScreenId: string): FormGroup {
    return this.subScreenForms[subScreenId] || this.fb.group({});
  }

  /**
   * Get SubScreen columns
   * EXACT COPY from component method (Lines 548-556)
   */
  getSubScreenColumns(subScreenId: string): any[][] {
    return this.subScreenColumns[subScreenId] || [];
  }

  /**
   * Get SubScreen column count
   * EXACT COPY from component method (Lines 557-570)
   */
  getSubScreenColumnCount(subScreenId: string): number {
    const metadata = this.subScreensMetadata.find(meta => meta.ID === subScreenId);
    return metadata?.columnNumber || 1;
  }

  /**
   * Get SubScreen multi array
   * EXACT COPY from component method (Lines 571-606)
   */
  getSubScreenMultiArray(subScreenId: string, fieldName: string, groupIndex?: number, groupName?: string, nestedGroupIndex?: number): FormArray {
    const subScreenForm = this.subScreenForms[subScreenId];
    if (!subScreenForm) {
      return this.fb.array([]);
    }

    if (groupIndex !== undefined && groupName) {
      // Check if this is a nested group
      const parsed = this.parseGroupPath(groupName);
      if (parsed.isNested && parsed.parent && parsed.child && nestedGroupIndex !== undefined) {
        // Navigate to nested group: parent[groupIndex].child[nestedGroupIndex].fieldName
        const parentArray = subScreenForm.get(parsed.parent) as FormArray;
        const parentGroup = parentArray.at(groupIndex) as FormGroup;
        const childArray = parentGroup.get(parsed.child) as FormArray;
        const childGroup = childArray.at(nestedGroupIndex) as FormGroup;
        return childGroup.get(fieldName) as FormArray;
      } else {
        // Regular group
        const groupArray = subScreenForm.get(groupName) as FormArray;
        const group = groupArray.at(groupIndex) as FormGroup;
        return group.get(fieldName) as FormArray;
      }
    } else {
      return subScreenForm.get(fieldName) as FormArray;
    }
  }

  /**
   * Add SubScreen multi field
   * EXACT COPY from component method (Lines 607-628)
   */
  addSubScreenMultiField(subScreenId: string, field: any, groupIndex?: number, index?: number, groupName?: string, nestedGroupIndex?: number) {
    try {
      const multiArray = this.getSubScreenMultiArray(subScreenId, field.fieldName, groupIndex, groupName, nestedGroupIndex);
      const newField = this.createMultiField(field);
      if (index !== undefined) {
        multiArray.insert(index + 1, newField);
      } else {
        multiArray.push(newField);
      }
    } catch (error) {
    }
  }

  /**
   * Remove SubScreen multi field
   * EXACT COPY from component method (Lines 629-639)
   */
  removeSubScreenMultiField(subScreenId: string, fieldName: string, index: number, groupIndex?: number, groupName?: string, nestedGroupIndex?: number) {
    const multiArray = this.getSubScreenMultiArray(subScreenId, fieldName, groupIndex, groupName, nestedGroupIndex);
    multiArray.removeAt(index);
  }

  /**
   * Get SubScreen group array
   * EXACT COPY from component method (Lines 640-653)
   */
  getSubScreenGroupArray(subScreenId: string, groupName: string): FormArray {
    const subScreenForm = this.subScreenForms[subScreenId];
    if (!subScreenForm) {
      return this.fb.array([]);
    }
    return subScreenForm.get(groupName) as FormArray;
  }

  /**
   * Get SubScreen fields for group
   * EXACT COPY from component method (Lines 654-667)
   */
  getSubScreenFieldsForGroup(subScreenId: string, groupName: string) {
    const subScreenMetadata = this.subScreensMetadata.find(meta => meta.ID === subScreenId);
    if (!subScreenMetadata || !subScreenMetadata.fieldName) {
      return [];
    }
    return subScreenMetadata.fieldName.filter((field: any) => field.Group && field.Group.trim() === groupName.trim());
  }

  /**
   * Check if field is first in parent group for SubScreen
   * EXACT COPY from component method (Lines 668-694)
   */
  isFirstFieldInParentGroupForSubScreen(subScreenId: string, field: any): boolean {
    if (!field.Group) return false;

    const parsed = this.parseGroupPath(field.Group);
    if (!parsed.parent) return false;

    const fieldsInGroup = this.getSubScreenFieldsForGroup(subScreenId, parsed.parent);
    if (fieldsInGroup.length === 0) return false;

    const firstField = fieldsInGroup[0];
    return firstField.fieldName === field.fieldName;
  }

  /**
   * Get SubScreen fields for group path
   * EXACT COPY from component method (Lines 695-708)
   */
  getSubScreenFieldsForGroupPath(subScreenId: string, groupPath: string) {
    const subScreenMetadata = this.subScreensMetadata.find(meta => meta.ID === subScreenId);
    if (!subScreenMetadata || !subScreenMetadata.fieldName) {
      return [];
    }
    return subScreenMetadata.fieldName.filter((field: any) => field.Group && field.Group.trim() === groupPath.trim());
  }

  /**
   * Get SubScreen nested group array
   * EXACT COPY from component method (Lines 709-729)
   */
  getSubScreenNestedGroupArray(subScreenId: string, groupPath: string, parentIndex?: number): FormArray {
    const subScreenForm = this.subScreenForms[subScreenId];
    if (!subScreenForm) {
      return this.fb.array([]);
    }

    const parsed = this.parseGroupPath(groupPath);
    if (parsed.isNested && parsed.parent && parsed.child) {
      const parentArray = subScreenForm.get(parsed.parent) as FormArray;
      if (parentIndex !== undefined && parentArray.at(parentIndex)) {
        const parentGroup = parentArray.at(parentIndex) as FormGroup;
        return parentGroup.get(parsed.child) as FormArray;
      }
    }

    return this.fb.array([]);
  }

  /**
   * Add SubScreen group
   * EXACT COPY from component method (Lines 730-747)
   */
  addSubScreenGroup(subScreenId: string, groupName: string, index?: number) {
    const groupArray = this.getSubScreenGroupArray(subScreenId, groupName);
    const subScreenMetadata = this.subScreensMetadata.find(meta => meta.ID === subScreenId);
    
    if (subScreenMetadata) {
      const newGroup = this.createSubScreenGroup(subScreenMetadata, groupName);
      if (index !== undefined) {
        groupArray.insert(index + 1, newGroup);
      } else {
        groupArray.push(newGroup);
      }
    }
  }

  /**
   * Remove SubScreen group
   * EXACT COPY from component method (Lines 748-757)
   */
  removeSubScreenGroup(subScreenId: string, groupName: string, index: number) {
    const groupArray = this.getSubScreenGroupArray(subScreenId, groupName);
    groupArray.removeAt(index);
  }

  /**
   * Clone SubScreen group
   * EXACT COPY from component method (Lines 758-797)
   */
  cloneSubScreenGroup(subScreenId: string, groupName: string, index: number): void {
    const groupArray = this.getSubScreenGroupArray(subScreenId, groupName);
    const groupToClone = groupArray.at(index) as FormGroup;

    // Step 1: Add a new empty group using your existing method
    this.addSubScreenGroup(subScreenId, groupName, index);

    // Step 2: Get the newly inserted group
    const clonedGroup = groupArray.at(index + 1) as FormGroup;

    // Step 3: Copy values (deep clone including nested FormArrays)
    Object.keys(groupToClone.controls).forEach(key => {
      const control = groupToClone.get(key);
      const clonedControl = clonedGroup.get(key);

      if (control instanceof FormArray && clonedControl instanceof FormArray) {
        clonedControl.clear();
        control.controls.forEach(c => {
          const clonedSubGroup = this.fb.group({});
          Object.keys((c as FormGroup).controls).forEach(subKey => {
            clonedSubGroup.addControl(subKey, this.fb.control((c as FormGroup).get(subKey)?.value));
          });
          clonedControl.push(clonedSubGroup);
        });
      } else {
        clonedControl?.setValue(control?.value);
      }
    });
  }

  /**
   * Add SubScreen nested group
   * EXACT COPY from component method (Lines 798-819)
   */
  addSubScreenNestedGroup(subScreenId: string, groupPath: string, parentIndex: number, index?: number) {
    const nestedGroupArray = this.getSubScreenNestedGroupArray(subScreenId, groupPath, parentIndex);
    const subScreenMetadata = this.subScreensMetadata.find(meta => meta.ID === subScreenId);
    
    if (subScreenMetadata) {
      const newNestedGroup = this.createSubScreenGroup(subScreenMetadata, groupPath);
      if (index !== undefined) {
        nestedGroupArray.insert(index + 1, newNestedGroup);
      } else {
        nestedGroupArray.push(newNestedGroup);
      }
    }
  }

  /**
   * Remove SubScreen nested group
   * EXACT COPY from component method (Lines 820-830)
   */
  removeSubScreenNestedGroup(subScreenId: string, groupPath: string, parentIndex: number, index: number) {
    const nestedGroupArray = this.getSubScreenNestedGroupArray(subScreenId, groupPath, parentIndex);
    nestedGroupArray.removeAt(index);
  }

  /**
   * Clone SubScreen nested group
   * EXACT COPY from component method (Lines 831-866)
   */
  cloneSubScreenNestedGroup(subScreenId: string, groupPath: string, parentIndex: number, index: number): void {
    const nestedGroupArray = this.getSubScreenNestedGroupArray(subScreenId, groupPath, parentIndex);
    const groupToClone = nestedGroupArray.at(index) as FormGroup;

    // Step 1: Add a new empty nested group using your existing method
    this.addSubScreenNestedGroup(subScreenId, groupPath, parentIndex, index);

    // Step 2: Get the newly inserted nested group
    const clonedGroup = nestedGroupArray.at(index + 1) as FormGroup;

    // Step 3: Copy values (deep clone including nested FormArrays)
    Object.keys(groupToClone.controls).forEach(key => {
      const control = groupToClone.get(key);
      const clonedControl = clonedGroup.get(key);

      if (control instanceof FormArray && clonedControl instanceof FormArray) {
        clonedControl.clear();
        control.controls.forEach(c => {
          const clonedSubGroup = this.fb.group({});
          Object.keys((c as FormGroup).controls).forEach(subKey => {
            clonedSubGroup.addControl(subKey, this.fb.control((c as FormGroup).get(subKey)?.value));
          });
          clonedControl.push(clonedSubGroup);
        });
      } else {
        clonedControl?.setValue(control?.value);
      }
    });
  }

  /**
   * Debug SubScreen fields
   * EXACT COPY from component method (Lines 867-875)
   */
  debugSubScreenFields(subScreenId: string) {
    const subScreenMetadata = this.subScreensMetadata.find(meta => meta.ID === subScreenId);
    if (subScreenMetadata && subScreenMetadata.fieldName) {
      console.log(`SubScreen ${subScreenId} fields:`, subScreenMetadata.fieldName);
    }
  }

  // Helper methods needed by SubScreen methods (copied from component)
  
  /**
   * Parse group path
   * EXACT COPY from component method
   */
  parseGroupPath(groupPath: string): { parent: string | null, child: string | null, isNested: boolean } {
    if (!groupPath || !groupPath.includes('|')) {
      return { parent: groupPath, child: null, isNested: false };
    }

    const parts = groupPath.split('|');
    if (parts.length === 2) {
      return { parent: parts[0].trim(), child: parts[1].trim(), isNested: true };
    }

    return { parent: groupPath, child: null, isNested: false };
  }

  // REMOVED: These methods are now in FieldDistributionService
  // orderFieldsBasedOnFormDefinition() - moved to FieldDistributionService
  // distributeFieldsRoundRobin() - moved to FieldDistributionService

  /**
   * Create multi field
   * EXACT COPY from component method
   */
  createMultiField(field: any): FormGroup {
    const group = this.fb.group({});
    if (field.fieldName) {
      const validators = field.mandatory ? Validators.required : null;
      let control;
      switch (field.type) {
        case "boolean":
          control = this.fb.control(false, validators);
          break;
        case "date":
          control = this.fb.control(null, validators);
          break;
        default:
          control = this.fb.control("", validators);
          break;
      }
      group.addControl(field.fieldName, control);
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
    return group;
  }

  /**
   * Add field to group
   * EXACT COPY from component method
   */
  addFieldToGroup(group: FormGroup, field: any): void {
    if (field.fieldName) {
      const validators = field.mandatory ? Validators.required : null;
      let control;
      switch (field.type) {
        case "boolean":
          control = this.fb.control(false, validators);
          break;
        case "date":
          control = this.fb.control(null, validators);
          break;
        default:
          control = this.fb.control("", validators);
          break;
      }
      group.addControl(field.fieldName, control);
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
  }
} 