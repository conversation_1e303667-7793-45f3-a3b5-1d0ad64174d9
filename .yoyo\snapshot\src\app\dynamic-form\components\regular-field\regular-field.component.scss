/* EXACT styles from main dynamic-form.component.scss lines 102-160, 309-330, 902-950, 1214-1400 */

/* Form Fields - EXACT from main component lines 102-160 */
.form-field {
  width: 100%;
  margin-bottom: 16px;
  position: relative;
}

.form-field label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 12px;
  color: #000000;
}

.form-field input,
.form-field select {
  width: 100%;
  max-width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid #ced4da;
  border-radius: 8px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  box-sizing: border-box;
}

/* Responsive input sizing - EXACT from main component lines 129-143 */
@media (max-width: 768px) {
  .form-field input,
  .form-field select {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: 10px 12px;
  }
}

@media (max-width: 480px) {
  .form-field input,
  .form-field select {
    padding: 8px 10px;
    font-size: 14px;
  }
}

.form-field input:focus,
.form-field select:focus {
  border-color: #9e9e9e;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

/* Form Input - EXACT from main component lines 309-330 */
.form-input {
  flex-grow: 1;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #222222;
  background: #FFFFFF;
  transition: all 0.3s ease;
  padding: 0 12px;
}

/* Override form-input background when disabled/readonly */
.form-input[disabled],
.form-input[readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

.form-input:focus {
  outline: none;
  border-color: #9e9e9e;
  box-shadow: 0 0 0 2px rgba(158, 158, 158, 0.25);
}

.form-input.invalid-input {
  border-color: #f44336;
  background-color: rgba(244, 67, 54, 0.05);
}

/* Dropdown styles are now handled by the unified DropdownComponent */

/* Checkbox styling - EXACT from main component lines 902-943 */
.form-field:has(input[type="checkbox"]) {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 8px !important;
}

.form-field:has(input[type="checkbox"]) label {
  margin-bottom: 0 !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  order: 2;
}

.form-field:has(input[type="checkbox"]) input[type="checkbox"] {
  order: 1;
  margin-right: 8px;
}

/* Required field indicator styling - EXACT from main component line 3064-3068 */
.form-field label span {
  color: #dc3545;
  font-weight: bold;
  margin-left: 2px;
}

/* No Input Indicator Styling - EXACT from main component lines 946-950 */
.no-input-indicator {
  color: #e74c3c;
  font-size: 11px;
  font-weight: normal;
  font-style: italic;
}

/* Unified background for disabled/readonly fields */
input[disabled],
select[disabled],
textarea[disabled],
input[readonly],
select[readonly],
textarea[readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

/* Higher specificity rules to override .form-field and .form-input styles */
.form-field input[disabled],
.form-field select[disabled],
.form-field textarea[disabled],
.form-field input[readonly],
.form-field select[readonly],
.form-field textarea[readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

/* Dropdown disabled/readonly styles are now handled by the unified DropdownComponent */

/* Additional protection for any potential edge cases with specific field types */
input[type="text"][disabled]:focus,
input[type="number"][disabled]:focus,
input[type="date"][disabled]:focus,
input[type="checkbox"][disabled]:focus,
input[type="text"][readonly]:focus,
input[type="number"][readonly]:focus,
input[type="date"][readonly]:focus,
input[type="checkbox"][readonly]:focus {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  border-color: #dadce0 !important;
  box-shadow: none !important;
  outline: none !important;
}

/* Ensure no user interaction is possible on disabled fields */
input[disabled],
select[disabled],
textarea[disabled] {
  pointer-events: none !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* Specific input type coverage for complete consistency */
input[type="text"][disabled],
input[type="number"][disabled],
input[type="date"][disabled],
input[type="checkbox"][disabled],
input[type="text"][readonly],
input[type="number"][readonly],
input[type="date"][readonly],
input[type="checkbox"][readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

/* Higher specificity for form field input types */
.form-field input[type="text"][disabled],
.form-field input[type="number"][disabled],
.form-field input[type="date"][disabled],
.form-field input[type="checkbox"][disabled],
.form-field input[type="text"][readonly],
.form-field input[type="number"][readonly],
.form-field input[type="date"][readonly],
.form-field input[type="checkbox"][readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

/* Hover state for disabled/readonly fields */
input[disabled]:hover,
select[disabled]:hover,
textarea[disabled]:hover,
input[readonly]:hover,
select[readonly]:hover,
textarea[readonly]:hover,
.form-field input[disabled]:hover,
.form-field select[disabled]:hover,
.form-field textarea[disabled]:hover,
.form-field input[readonly]:hover,
.form-field select[readonly]:hover,
.form-field textarea[readonly]:hover {
  background-color: #f1f3f4 !important;
  border-color: #dadce0 !important;
}

/* Focus state for disabled/readonly fields */
input[disabled]:focus,
select[disabled]:focus,
textarea[disabled]:focus,
input[readonly]:focus,
select[readonly]:focus,
textarea[readonly]:focus,
.form-field input[disabled]:focus,
.form-field select[disabled]:focus,
.form-field textarea[disabled]:focus,
.form-field input[readonly]:focus,
.form-field select[readonly]:focus,
.form-field textarea[readonly]:focus {
  background-color: #f1f3f4 !important;
  border-color: #dadce0 !important;
  box-shadow: none !important;
  outline: none !important;
}



/* Responsive no-input indicator - EXACT from backup lines 993-999 */
@media (max-width: 768px) {
  .no-input-indicator {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .no-input-indicator {
    font-size: 9px;
  }
}

/* Dropdown responsive styles are now handled by the unified DropdownComponent */

/* Checkbox label alignment fixes - EXACT from backup */
input[type="checkbox"] {
  margin-right: 8px;
  vertical-align: middle;
  width: auto !important;
  flex-shrink: 0;
}

.form-field:has(input[type="checkbox"]) {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 8px !important;
}

.form-field:has(input[type="checkbox"]) label {
  margin-bottom: 0 !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  order: 2;
}

.form-field:has(input[type="checkbox"]) input[type="checkbox"] {
  order: 1;
  margin-right: 8px;
}

/* Responsive checkbox alignment */
@media (max-width: 768px) {
  .form-field:has(input[type="checkbox"]) {
    flex-direction: row !important;
    align-items: center !important;
    gap: 6px !important;
  }

  input[type="checkbox"] {
    margin-right: 6px;
  }
}

@media (max-width: 480px) {
  .form-field:has(input[type="checkbox"]) {
    gap: 4px !important;
  }

  input[type="checkbox"] {
    margin-right: 4px;
  }
}
