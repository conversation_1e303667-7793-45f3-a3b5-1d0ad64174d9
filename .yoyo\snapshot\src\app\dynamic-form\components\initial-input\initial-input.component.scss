@use '../../../../styles/shared.scss' as *;

.form-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  background-color: white;
}

.form-main-field {
  display: flex;
  align-items: center;
  gap: 24px;
  width: 100%;
  padding-top: 16px;
  background-color: white;
}

.form-label {
  width: 160px;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-size: 18px;
  color: #000000;
  padding-left: 24px;
}

.input-button-group {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-grow: 1;
  width: 100%;
  padding-right: 24px;
  flex-wrap: wrap;
  min-width: 0;
}

.form-input {
  flex-grow: 1;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #222222;
  background: #FFFFFF;
  transition: all 0.3s ease;
  padding: 0 12px;
  
  &:focus {
    outline: none;
    border-color: #9e9e9e;
    box-shadow: 0 0 0 2px rgba(158, 158, 158, 0.25);
  }
  
  &.invalid-input {
    border-color: #f44336;
    background-color: rgba(244, 67, 54, 0.05);
    
    &:focus {
      box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
    }
  }
  
  &::placeholder {
    color: #999;
  }
}

/* ID Input Container - Dropdown styles are now handled by the unified DropdownComponent */
.id-input-container {
  position: relative;
  display: flex;
  align-items: center;
  flex-grow: 1;
  width: 100%;
  min-width: 0;
  flex: 1;
}

/* Ensure dropdown component takes full width in initial-input context */
.id-input-container app-dropdown {
  width: 100%;
  display: block;
}

.id-input-container app-dropdown ::ng-deep .dropdown-input-container {
  width: 100%;
  min-width: 100%;
}

/* Responsive dropdown styles */
@media (max-width: 768px) {
  .id-input-container app-dropdown ::ng-deep .dropdown-input-container {
    min-width: 0;
    width: 100%;
  }
  
  .id-input-container app-dropdown ::ng-deep .form-input {
    font-size: 14px;
    padding: 0 8px;
  }
  
  /* Ensure buttons wrap properly */
  .input-button-group {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .id-input-container app-dropdown ::ng-deep .form-input {
    font-size: 13px;
    padding: 0 6px;
    height: 36px;
  }
  
  .id-input-container app-dropdown ::ng-deep .dropdown-arrow-btn {
    width: 36px;
    height: 36px;
  }
}

@media (max-width: 360px) {
  .id-input-container app-dropdown ::ng-deep .form-input {
    font-size: 12px;
    padding: 0 4px;
    height: 32px;
  }
  
  .id-input-container app-dropdown ::ng-deep .dropdown-arrow-btn {
    width: 32px;
    height: 32px;
  }
}

/* Button Container with Rounded Corners */
.button-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Enhanced Initial Input Button Styles */
.initial-input-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 2px solid;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
  min-width: 36px;
  min-height: 36px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  
  mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    margin: 0;
    padding: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

/* Add Button - Green theme */
.initial-input-button.add-button {
  border-color: #4CAF50;
  background-color: white;
  color: #4CAF50;
  
  &:hover {
    background-color: #4CAF50;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
  }
}

/* Edit Button - Blue theme */
.initial-input-button.edit-button {
  border-color: #2196F3;
  background-color: white;
  color: #2196F3;
  
  &:hover {
    background-color: #2196F3;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
  }
}

/* View Button - Orange theme */
.initial-input-button.view-button {
  border-color: #FF9800;
  background-color: white;
  color: #FF9800;
  
  &:hover {
    background-color: #FF9800;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 152, 0, 0.3);
  }
}

/* Maintenance Button - Red theme */
.initial-input-button.maintenance-button {
  border-color: #F44336;
  background-color: white;
  color: #F44336;
  
  &:hover {
    background-color: #F44336;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(244, 67, 54, 0.3);
  }
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .form-main-field {
    gap: 16px;
    padding: 16px 12px;
  }
  
  .form-label {
    width: 120px;
    font-size: 16px;
    padding-left: 12px;
  }
  
  .input-button-group {
    padding-right: 12px;
    gap: 8px;
    flex-wrap: wrap;
  }
  
  .id-input-container {
    min-width: 200px;
    flex: 1;
  }
  
  .button-container {
    padding: 6px 10px;
    gap: 6px;
  }
  
  .initial-input-button {
    width: 32px;
    height: 32px;
    min-width: 32px;
    min-height: 32px;
    
    mat-icon {
      font-size: 14px;
      width: 14px;
      height: 14px;
      line-height: 1;
      margin: 0;
      padding: 0;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}

@media (max-width: 768px) {
  .form-main-field {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 12px 8px;
  }
  
  .form-label {
    width: 100%;
    padding-left: 0;
    text-align: center;
    font-size: 16px;
  }
  
  .input-button-group {
    padding-right: 0;
    justify-content: center;
    flex-wrap: wrap;
    width: 100%;
    gap: 6px;
  }
  
  .id-input-container {
    width: 100%;
    min-width: 0;
    flex: 1;
    margin-bottom: 8px;
  }
  
  .button-container {
    padding: 6px 8px;
    gap: 4px;
    justify-content: center;
  }
  
  .initial-input-button {
    width: 32px;
    height: 32px;
    min-width: 32px;
    min-height: 32px;
    flex-shrink: 0;
    
    mat-icon {
      font-size: 14px;
      width: 14px;
      height: 14px;
      line-height: 1;
      margin: 0;
      padding: 0;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}

@media (max-width: 480px) {
  .form-main-field {
    padding: 8px 4px;
    gap: 8px;
  }
  
  .form-label {
    font-size: 14px;
    padding: 4px 0;
  }
  
  .input-button-group {
    gap: 4px;
    padding: 0 4px;
    justify-content: space-between;
  }
  
  .id-input-container {
    min-width: 0;
    flex: 1;
    margin-bottom: 6px;
  }
  
  .button-container {
    padding: 4px 6px;
    gap: 3px;
  }
  
  .initial-input-button {
    width: 28px;
    height: 28px;
    min-width: 28px;
    min-height: 28px;
    
    mat-icon {
      font-size: 12px;
      width: 12px;
      height: 12px;
      line-height: 1;
      margin: 0;
      padding: 0;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}

@media (max-width: 360px) {
  .form-main-field {
    padding: 6px 2px;
  }
  
  .form-label {
    font-size: 13px;
  }
  
  .input-button-group {
    gap: 3px;
    padding: 0 2px;
    justify-content: space-between;
  }
  
  .id-input-container {
    margin-bottom: 4px;
  }
  
  .button-container {
    padding: 3px 4px;
    gap: 2px;
  }
  
  .initial-input-button {
    width: 24px;
    height: 24px;
    min-width: 24px;
    min-height: 24px;
    
    mat-icon {
      font-size: 10px;
      width: 10px;
      height: 10px;
      line-height: 1;
      margin: 0;
      padding: 0;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}
