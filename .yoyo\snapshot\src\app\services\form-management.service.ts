import { Injectable, inject } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators, FormControl, AbstractControl } from "@angular/forms";

@Injectable({
  providedIn: 'root'
})
export class FormManagementService {
  private fb = inject(FormBuilder);

  /**
   * Initialize form - EXACT original logic
   */
  initializeForm(): FormGroup {
    return this.fb.group({
      ID: ["", Validators.required],
    });
  }

  /**
   * Set form readonly - EXACT original logic
   */
  setFormReadonly(form: FormGroup, fields: any[], getGroupArray: (groupName: string) => FormArray, isReadonly: boolean): void {
    const disableControls = (control: AbstractControl) => {
      if (control instanceof FormGroup) {
        Object.values(control.controls).forEach(disableControls);
      } else if (control instanceof FormArray) {
        control.controls.forEach(disableControls);
      } else {
        if (isReadonly) {
          control.disable({ emitEvent: false });
        } else {
          control.enable({ emitEvent: false });
        }
      }
    };

    disableControls(form);

    // Explicitly handle isMulti fields
    fields.forEach((field) => {
      if (field.isMulti) {
        const formArray = form.get(field.fieldName) as FormArray;
        if (formArray) {
          formArray.controls.forEach((control) => {
            if (isReadonly) {
              control.disable({ emitEvent: false });
            } else {
              control.enable({ emitEvent: false });
            }
          });
        }
      }

      // Explicitly handle grouped fields
      if (field.Group) {
        const groupArray = getGroupArray(field.Group);
        if (groupArray) {
          groupArray.controls.forEach((control) => {
            if (isReadonly) {
              control.disable({ emitEvent: false });
            } else {
              control.enable({ emitEvent: false });
            }
          });
        }
      }
    });
  }

  /**
   * Build form - EXACT original logic
   */
  buildForm(fields: any[], form: FormGroup, getParentGroups: () => string[], addGroup: (groupName: string) => void): void {
    const groupedFields: { [key: string]: FormArray } = {};

    // First, create all parent groups
    const parentGroups = getParentGroups();
    parentGroups.forEach(parentGroup => {
      if (!groupedFields[parentGroup]) {
        groupedFields[parentGroup] = this.fb.array([]);
        form.addControl(parentGroup, groupedFields[parentGroup]);
        addGroup(parentGroup);
      }
    });

    fields.forEach((field) => {
      if (field.fieldName !== "ID") {
        if (field.isMulti && !field.Group) {
          // Non-grouped multi-field
          const multiFieldArray = this.fb.array([this.createMultiField(field)]);
          form.addControl(field.fieldName, multiFieldArray);

          // Disable multi-field if noInput is true
          if (field.noInput) {
            multiFieldArray.disable({ emitEvent: false });
          }
        } else if (field.Group) {
          const parsed = this.parseGroupPath(field.Group);
          if (!parsed.isNested) {
            // Direct field of parent group - already handled in createGroup
          } else {
            // Nested group field - already handled in createGroup
          }
        } else {
          // Non-grouped regular field
          const validators = field.mandatory ? Validators.required : null;
          let control;
          switch (field.type) {
            case "boolean":
              control = this.fb.control(false, validators);
              break;
            case "date":
              control = this.fb.control(null, validators);
              break;
            default:
              control = this.fb.control("", validators);
              break;
          }
          form.addControl(field.fieldName, control);

          // Disable control if noInput is true
          if (field.noInput) {
            control.disable({ emitEvent: false });
          }
        }
      }
    });

    console.log('🔍 BUILD: Form built successfully');
    console.log('🔍 BUILD: Form controls:', Object.keys(form.controls));
  }

  /**
   * Create group - EXACT original logic
   */
  createGroup(groupName: string, getFieldsForGroup: (groupName: string) => any[], getFieldsForGroupPath: (groupPath: string) => any[], getChildGroups: (groupName: string) => string[], addFieldToGroup: (group: FormGroup, field: any) => void): FormGroup {
    const group = this.fb.group({});

    // Handle nested groups
    const parsed = this.parseGroupPath(groupName);
    if (parsed.isNested && parsed.parent && parsed.child) {
      // This is a nested group, create fields for this specific path
      getFieldsForGroupPath(groupName).forEach((field) => {
        addFieldToGroup(group, field);
      });
    } else {
      // This is a parent group, create fields and nested subgroups
      getFieldsForGroup(groupName).forEach((field) => {
        const fieldParsed = this.parseGroupPath(field.Group);
        if (!fieldParsed.isNested) {
          // Direct field of this group
          addFieldToGroup(group, field);
        }
      });

      // Add nested subgroups
      const childGroups = getChildGroups(groupName);
      childGroups.forEach(childGroup => {
        const childGroupArray = this.fb.array([this.createGroup(`${groupName}|${childGroup}`, getFieldsForGroup, getFieldsForGroupPath, getChildGroups, addFieldToGroup)]);
        group.addControl(childGroup, childGroupArray);
      });
    }

    return group;
  }

  /**
   * Add field to group - EXACT original logic
   */
  addFieldToGroup(group: FormGroup, field: any): void {
    if (field.isMulti) {
      const multiFieldArray = this.fb.array([this.createMultiField(field)]);
      group.addControl(field.fieldName, multiFieldArray);

      // Disable multi-field if noInput is true
      if (field.noInput) {
        multiFieldArray.disable({ emitEvent: false });
      }
    } else if (field.foreginKey) {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    } else {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
  }

  /**
   * Create multi field - EXACT original logic
   */
  createMultiField(field: any): FormGroup {
    const group = this.fb.group({});
    if (Array.isArray(field)) {
      field.forEach(fieldName => {
        const control = this.fb.control('', Validators.required);
        group.addControl(fieldName, control);
      });
    } else {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
    return group;
  }

  /**
   * Create multi field 2 - EXACT original logic
   */
  createMultiField2(fieldMeta: any, sampleData?: any): FormGroup {
    const group = this.fb.group({});
    if (sampleData) {
      Object.keys(sampleData).forEach(key => {
        const control = this.fb.control(sampleData[key]);
        group.addControl(key, control);
      });
    } else {
      const control = this.fb.control("", fieldMeta.mandatory ? Validators.required : null);
      group.addControl(fieldMeta.fieldName, control);
    }
    return group;
  }

  /**
   * Parse group path - EXACT original logic
   */
  parseGroupPath(groupPath: string): { parent: string | null, child: string | null, isNested: boolean } {
    if (!groupPath) {
      return { parent: null, child: null, isNested: false };
    }

    const parts = groupPath.split('|');
    if (parts.length === 2) {
      return {
        parent: parts[0].trim(),
        child: parts[1].trim(),
        isNested: true
      };
    } else {
      return {
        parent: groupPath.trim(),
        child: null,
        isNested: false
      };
    }
  }

  /**
   * Clean up form - EXACT original logic
   */
  cleanupForm(form: FormGroup): void {
    if (form) {
      Object.keys(form.controls).forEach(key => {
        const control = form.get(key);
        if (control instanceof FormArray) {
          control.clear();
        } else if (control instanceof FormGroup) {
          this.cleanupForm(control);
        }
      });
    }
  }
} 