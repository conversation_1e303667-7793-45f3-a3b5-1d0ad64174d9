import { Injectable, inject } from '@angular/core';
import { FormBuilder, FormGroup, FormArray } from "@angular/forms";
import { FormManagementService } from './form-management.service';

@Injectable({
  providedIn: 'root'
})
export class MultiFieldManagementService {
  private fb = inject(FormBuilder);
  private formManagementService = inject(FormManagementService);

  /**
   * Get multi array - EXACT original logic
   */
  getMultiArray(form: FormGroup, fieldName: string, groupIndex: number | undefined, groupName: string | undefined, nestedGroupIndex: number | undefined): FormArray {
    if (groupIndex !== undefined && groupName) {
      // Check if this is a nested group
      const parsed = this.formManagementService.parseGroupPath(groupName);
      if (parsed.isNested && parsed.parent && parsed.child && nestedGroupIndex !== undefined) {
        // Navigate to nested group: parent[groupIndex].child[nestedGroupIndex].fieldName
        const parentArray = form.get(parsed.parent) as FormArray;
        const parentGroup = parentArray.at(groupIndex) as FormGroup;
        const childArray = parentGroup.get(parsed.child) as FormArray;
        const childGroup = childArray.at(nestedGroupIndex) as FormGroup;
        return childGroup.get(fieldName) as FormArray;
      } else {
        // Regular group
        const groupArray = form.get(groupName) as FormArray;
        const group = groupArray.at(groupIndex) as FormGroup;
        return group.get(fieldName) as FormArray;
      }
    } else {
      return form.get(fieldName) as FormArray;
    }
  }

  /**
   * Add multi field - EXACT original logic
   */
  addMultiField(form: FormGroup, field: any, groupIndex: number | undefined, index: number | undefined, groupName: string | undefined, nestedGroupIndex: number | undefined): void {
    try {
      const multiArray = this.getMultiArray(form, field.fieldName, groupIndex, groupName, nestedGroupIndex);
      const newField = this.formManagementService.createMultiField(field);
      
      if (index !== undefined) {
        multiArray.insert(index + 1, newField);
      } else {
        multiArray.push(newField);
      }
    } catch (error) {
      // Handle error silently
    }
  }

  /**
   * Remove multi field - EXACT original logic
   */
  removeMultiField(form: FormGroup, fieldName: string, index: number, groupIndex: number | undefined, groupName: string | undefined, nestedGroupIndex: number | undefined): void {
    const multiArray = this.getMultiArray(form, fieldName, groupIndex, groupName, nestedGroupIndex);
    multiArray.removeAt(index);
  }

  /**
   * Get SubScreen multi array - EXACT original logic
   */
  getSubScreenMultiArray(subScreenForm: FormGroup, fieldName: string, groupIndex: number | undefined, groupName: string | undefined, nestedGroupIndex: number | undefined): FormArray {
    if (!subScreenForm) {
      return this.fb.array([]);
    }

    if (groupIndex !== undefined && groupName) {
      // Check if this is a nested group
      const parsed = this.formManagementService.parseGroupPath(groupName);
      if (parsed.isNested && parsed.parent && parsed.child && nestedGroupIndex !== undefined) {
        // Navigate to nested group: parent[groupIndex].child[nestedGroupIndex].fieldName
        const parentArray = subScreenForm.get(parsed.parent) as FormArray;
        const parentGroup = parentArray.at(groupIndex) as FormGroup;
        const childArray = parentGroup.get(parsed.child) as FormArray;
        const childGroup = childArray.at(nestedGroupIndex) as FormGroup;
        return childGroup.get(fieldName) as FormArray;
      } else {
        // Regular group
        const groupArray = subScreenForm.get(groupName) as FormArray;
        const group = groupArray.at(groupIndex) as FormGroup;
        return group.get(fieldName) as FormArray;
      }
    } else {
      return subScreenForm.get(fieldName) as FormArray;
    }
  }

  /**
   * Add SubScreen multi field - EXACT original logic
   */
  addSubScreenMultiField(subScreenForm: FormGroup, field: any, groupIndex: number | undefined, index: number | undefined, groupName: string | undefined, nestedGroupIndex: number | undefined): void {
    try {
      const multiArray = this.getSubScreenMultiArray(subScreenForm, field.fieldName, groupIndex, groupName, nestedGroupIndex);
      const newField = this.formManagementService.createMultiField(field);
      
      if (index !== undefined) {
        multiArray.insert(index + 1, newField);
      } else {
        multiArray.push(newField);
      }
    } catch (error) {
      // Handle error silently
    }
  }

  /**
   * Remove SubScreen multi field - EXACT original logic
   */
  removeSubScreenMultiField(subScreenForm: FormGroup, fieldName: string, index: number, groupIndex: number | undefined, groupName: string | undefined, nestedGroupIndex: number | undefined): void {
    const multiArray = this.getSubScreenMultiArray(subScreenForm, fieldName, groupIndex, groupName, nestedGroupIndex);
    multiArray.removeAt(index);
  }
} 