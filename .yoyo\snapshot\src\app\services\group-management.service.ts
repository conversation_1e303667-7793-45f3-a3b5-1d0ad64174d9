import { Injectable, inject } from '@angular/core';
import { FormBuilder, FormGroup, FormArray } from "@angular/forms";
import { FormManagementService } from './form-management.service';

@Injectable({
  providedIn: 'root'
})
export class GroupManagementService {
  private fb = inject(FormBuilder);
  private formManagementService = inject(FormManagementService);

  /**
   * Get group array - EXACT original logic
   */
  getGroupArray(form: FormGroup, groupName: string): FormArray {
    return form.get(groupName) as FormArray;
  }

  /**
   * Get nested group array - EXACT original logic
   */
  getNestedGroupArray(form: FormGroup, groupPath: string, parentIndex: number | undefined): FormArray {
    const parsed = this.formManagementService.parseGroupPath(groupPath);
    if (parsed.isNested && parsed.parent && parsed.child && parentIndex !== undefined) {
      const parentArray = form.get(parsed.parent) as FormArray;
      const parentGroup = parentArray.at(parentIndex) as FormGroup;
      return parentGroup.get(parsed.child) as FormArray;
    }
    return form.get(groupPath) as FormArray;
  }

  /**
   * Add group - EXACT original logic
   */
  addGroup(form: FormGroup, groupName: string, index: number | undefined, createGroup: (groupName: string) => FormGroup): void {
    const groupArray = this.getGroupArray(form, groupName);
    const newGroup = createGroup(groupName);
    
    if (index !== undefined) {
      groupArray.insert(index + 1, newGroup);
    } else {
      groupArray.push(newGroup);
    }
  }

  /**
   * Remove group - EXACT original logic
   */
  removeGroup(form: FormGroup, groupName: string, index: number): void {
    this.getGroupArray(form, groupName).removeAt(index);
  }

  /**
   * Clone group - EXACT original logic
   */
  cloneGroup(form: FormGroup, groupName: string, index: number, createGroup: (groupName: string) => FormGroup): void {
    const groupArray = this.getGroupArray(form, groupName);
    const groupToClone = groupArray.at(index) as FormGroup;

    // Add a new empty group using your existing method
    this.addGroup(form, groupName, index, createGroup);

    // Get the newly created group (it's at index + 1)
    const newGroup = groupArray.at(index + 1) as FormGroup;

    // Clone all values from the original group to the new group
    Object.keys(groupToClone.controls).forEach(key => {
      const originalControl = groupToClone.get(key);
      const newControl = newGroup.get(key);
      
      if (originalControl && newControl) {
        if (originalControl instanceof FormArray && newControl instanceof FormArray) {
          // Handle FormArray cloning
          originalControl.controls.forEach((control, i) => {
            if (i < newControl.length) {
              newControl.at(i).patchValue(control.value);
            }
          });
        } else {
          // Handle regular FormControl cloning
          newControl.patchValue(originalControl.value);
        }
      }
    });
  }

  /**
   * Add nested group - EXACT original logic
   */
  addNestedGroup(form: FormGroup, groupPath: string, parentIndex: number, index: number | undefined, createGroup: (groupName: string) => FormGroup): void {
    const parsed = this.formManagementService.parseGroupPath(groupPath);
    if (parsed.isNested && parsed.parent && parsed.child) {
      const nestedArray = this.getNestedGroupArray(form, groupPath, parentIndex);
      const newGroup = createGroup(parsed.child);
      
      if (index !== undefined) {
        nestedArray.insert(index + 1, newGroup);
      } else {
        nestedArray.push(newGroup);
      }
    }
  }

  /**
   * Remove nested group - EXACT original logic
   */
  removeNestedGroup(form: FormGroup, groupPath: string, parentIndex: number, index: number): void {
    this.getNestedGroupArray(form, groupPath, parentIndex).removeAt(index);
  }

  /**
   * Clone nested group - EXACT original logic
   */
  cloneNestedGroup(form: FormGroup, groupPath: string, parentIndex: number, index: number, createGroup: (groupName: string) => FormGroup): void {
    const parsed = this.formManagementService.parseGroupPath(groupPath);
    if (parsed.isNested && parsed.parent && parsed.child) {
      const nestedArray = this.getNestedGroupArray(form, groupPath, parentIndex);
      const groupToClone = nestedArray.at(index) as FormGroup;

      // Add a new empty nested group
      this.addNestedGroup(form, groupPath, parentIndex, index, createGroup);

      // Get the newly created group (it's at index + 1)
      const newGroup = nestedArray.at(index + 1) as FormGroup;

      // Clone all values from the original group to the new group
      Object.keys(groupToClone.controls).forEach(key => {
        const originalControl = groupToClone.get(key);
        const newControl = newGroup.get(key);
        
        if (originalControl && newControl) {
          if (originalControl instanceof FormArray && newControl instanceof FormArray) {
            // Handle FormArray cloning
            originalControl.controls.forEach((control, i) => {
              if (i < newControl.length) {
                newControl.at(i).patchValue(control.value);
              }
            });
          } else {
            // Handle regular FormControl cloning
            newControl.patchValue(originalControl.value);
          }
        }
      });
    }
  }

  /**
   * Get fields for group - EXACT original logic
   */
  getFieldsForGroup(fields: any[], groupName: string): any[] {
    return fields.filter(field => field.Group && field.Group.trim() === groupName.trim());
  }

  /**
   * Get fields for group path - EXACT original logic
   */
  getFieldsForGroupPath(fields: any[], groupPath: string): any[] {
    return fields.filter(field => field.Group && field.Group.trim() === groupPath.trim());
  }

  /**
   * Get parent groups - EXACT original logic
   */
  getParentGroups(fields: any[]): string[] {
    const groups = new Set<string>();
    
    fields.forEach(field => {
      if (field.Group) {
        const parsed = this.formManagementService.parseGroupPath(field.Group);
        if (parsed.parent) {
          groups.add(parsed.parent);
        }
      }
    });
    
    return Array.from(groups);
  }

  /**
   * Get child groups - EXACT original logic
   */
  getChildGroups(fields: any[], parentGroup: string): string[] {
    const childGroups = new Set<string>();
    
    fields.forEach(field => {
      if (field.Group) {
        const parsed = this.formManagementService.parseGroupPath(field.Group);
        if (parsed.isNested && parsed.parent === parentGroup && parsed.child) {
          childGroups.add(parsed.child);
        }
      }
    });
    
    return Array.from(childGroups);
  }

  /**
   * Check if first field in group - EXACT original logic
   */
  isFirstFieldInGroup(fields: any[], field: any): boolean {
    if (!field.Group) return false;
    
    const groupFields = this.getFieldsForGroup(fields, field.Group);
    return groupFields.indexOf(field) === 0;
  }

  /**
   * Check if first field in parent group - EXACT original logic
   */
  isFirstFieldInParentGroup(fields: any[], field: any): boolean {
    if (!field.Group) return false;
    
    const parsed = this.formManagementService.parseGroupPath(field.Group);
    if (!parsed.parent) return false;
    
    const parentGroupFields = fields.filter(f => {
      if (!f.Group) return false;
      const fParsed = this.formManagementService.parseGroupPath(f.Group);
      return fParsed.parent === parsed.parent;
    });
    
    return parentGroupFields.indexOf(field) === 0;
  }

  /**
   * Check if first field in nested group - EXACT original logic
   */
  isFirstFieldInNestedGroup(fields: any[], field: any): boolean {
    if (!field.Group) return false;
    
    const parsed = this.formManagementService.parseGroupPath(field.Group);
    if (!parsed.isNested || !parsed.parent || !parsed.child) return false;
    
    const nestedGroupFields = this.getFieldsForGroupPath(fields, field.Group);
    return nestedGroupFields.indexOf(field) === 0;
  }
} 